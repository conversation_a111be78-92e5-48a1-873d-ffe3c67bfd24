package com.ylkj.system.service;

import java.util.Map;

/**
 * 主图更新统计服务接口
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
public interface IMainImageUpdateStatsService {
    
    /**
     * 记录处理开始
     * 
     * @param taskId 任务ID
     * @param totalCount 总商品数
     */
    void recordProcessStart(String taskId, int totalCount);
    
    /**
     * 记录处理完成
     * 
     * @param taskId 任务ID
     * @param successCount 成功数
     * @param failedCount 失败数
     * @param skippedCount 跳过数
     */
    void recordProcessComplete(String taskId, int successCount, int failedCount, int skippedCount);
    
    /**
     * 记录单个商品处理结果
     * 
     * @param taskId 任务ID
     * @param sku 商品SKU
     * @param success 是否成功
     * @param duration 处理耗时（毫秒）
     * @param errorMessage 错误信息（如果失败）
     */
    void recordProductResult(String taskId, String sku, boolean success, long duration, String errorMessage);
    
    /**
     * 获取任务统计信息
     * 
     * @param taskId 任务ID
     * @return 统计信息
     */
    TaskStats getTaskStats(String taskId);
    
    /**
     * 获取总体统计信息
     * 
     * @return 总体统计信息
     */
    OverallStats getOverallStats();
    
    /**
     * 清理过期统计数据
     * 
     * @param daysToKeep 保留天数
     */
    void cleanupExpiredStats(int daysToKeep);
    
    /**
     * 任务统计信息
     */
    class TaskStats {
        private String taskId;
        private long startTime;
        private long endTime;
        private int totalCount;
        private int successCount;
        private int failedCount;
        private int skippedCount;
        private long totalDuration;
        private double averageDuration;
        private double successRate;
        
        // Getters and Setters
        public String getTaskId() { return taskId; }
        public void setTaskId(String taskId) { this.taskId = taskId; }
        
        public long getStartTime() { return startTime; }
        public void setStartTime(long startTime) { this.startTime = startTime; }
        
        public long getEndTime() { return endTime; }
        public void setEndTime(long endTime) { this.endTime = endTime; }
        
        public int getTotalCount() { return totalCount; }
        public void setTotalCount(int totalCount) { this.totalCount = totalCount; }
        
        public int getSuccessCount() { return successCount; }
        public void setSuccessCount(int successCount) { this.successCount = successCount; }
        
        public int getFailedCount() { return failedCount; }
        public void setFailedCount(int failedCount) { this.failedCount = failedCount; }
        
        public int getSkippedCount() { return skippedCount; }
        public void setSkippedCount(int skippedCount) { this.skippedCount = skippedCount; }
        
        public long getTotalDuration() { return totalDuration; }
        public void setTotalDuration(long totalDuration) { this.totalDuration = totalDuration; }
        
        public double getAverageDuration() { return averageDuration; }
        public void setAverageDuration(double averageDuration) { this.averageDuration = averageDuration; }
        
        public double getSuccessRate() { return successRate; }
        public void setSuccessRate(double successRate) { this.successRate = successRate; }
    }
    
    /**
     * 总体统计信息
     */
    class OverallStats {
        private long totalTasks;
        private long totalProducts;
        private long totalSuccessProducts;
        private long totalFailedProducts;
        private long totalSkippedProducts;
        private double overallSuccessRate;
        private double averageTaskDuration;
        private double averageProductDuration;
        private Map<String, Long> platformStats;
        private Map<String, Long> errorStats;
        
        // Getters and Setters
        public long getTotalTasks() { return totalTasks; }
        public void setTotalTasks(long totalTasks) { this.totalTasks = totalTasks; }
        
        public long getTotalProducts() { return totalProducts; }
        public void setTotalProducts(long totalProducts) { this.totalProducts = totalProducts; }
        
        public long getTotalSuccessProducts() { return totalSuccessProducts; }
        public void setTotalSuccessProducts(long totalSuccessProducts) { this.totalSuccessProducts = totalSuccessProducts; }
        
        public long getTotalFailedProducts() { return totalFailedProducts; }
        public void setTotalFailedProducts(long totalFailedProducts) { this.totalFailedProducts = totalFailedProducts; }
        
        public long getTotalSkippedProducts() { return totalSkippedProducts; }
        public void setTotalSkippedProducts(long totalSkippedProducts) { this.totalSkippedProducts = totalSkippedProducts; }
        
        public double getOverallSuccessRate() { return overallSuccessRate; }
        public void setOverallSuccessRate(double overallSuccessRate) { this.overallSuccessRate = overallSuccessRate; }
        
        public double getAverageTaskDuration() { return averageTaskDuration; }
        public void setAverageTaskDuration(double averageTaskDuration) { this.averageTaskDuration = averageTaskDuration; }
        
        public double getAverageProductDuration() { return averageProductDuration; }
        public void setAverageProductDuration(double averageProductDuration) { this.averageProductDuration = averageProductDuration; }
        
        public Map<String, Long> getPlatformStats() { return platformStats; }
        public void setPlatformStats(Map<String, Long> platformStats) { this.platformStats = platformStats; }
        
        public Map<String, Long> getErrorStats() { return errorStats; }
        public void setErrorStats(Map<String, Long> errorStats) { this.errorStats = errorStats; }
    }
}
