package com.ylkj.service.impl.omg;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ylkj.controller.GoodsController;
import com.ylkj.mapper.ProductImagesMapper;
import com.ylkj.mapper.ProductMainImagesMapper;
import com.ylkj.mapper.UsersMapper;
import com.ylkj.mapper.omg.OCommentsMapper;
import com.ylkj.mapper.omg.OProductImagesMapper;
import com.ylkj.model.domain.*;
import com.ylkj.model.domain.omg.OComments;
import com.ylkj.model.domain.omg.OProductImages;
import com.ylkj.model.domain.omg.OProducts;
import com.ylkj.mapper.omg.OProductsMapper;
import com.ylkj.model.domain.qcdomain.GoodsDetail;
import com.ylkj.model.domain.qcdomain.QcItem;
import com.ylkj.model.enums.CacheUpdateType;
import com.ylkj.model.vo.OProductsVO;
import com.ylkj.model.vo.OSkuProductsVO;
import com.ylkj.model.vo.ProductMainImagesVO;
import com.ylkj.model.vo.ProductsVo;
import com.ylkj.service.impl.OkHttpGoodsService;
import com.ylkj.service.impl.ProductsServiceImpl;
import com.ylkj.service.omg.OIProductsService;
import com.ylkj.service.omg.AsyncQcUpdateService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylkj.common.core.redis.RedisCache;
import com.ylkj.common.constant.CacheConstants;
import lombok.var;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.constraints.Null;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * omg_商品表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@Service
public class OProductsServiceImpl extends ServiceImpl<OProductsMapper, OProducts> implements OIProductsService {
    private static final Logger log = LoggerFactory.getLogger(ProductsServiceImpl.class);

    @Autowired
    private OProductsMapper oProductsMapper;

    @Autowired
    private OProductImagesMapper oProductImagesMapper;

    @Autowired
    private UsersMapper usersMapper;

    @Autowired
    private OkHttpGoodsService okHttpGoodsService;

    @Autowired
    private OCommentsMapper commentsMapper;

    @Autowired
    private GoodsController goodsController;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private ProductMainImagesMapper productMainImagesMapper;

    @Autowired
    private AsyncQcUpdateService asyncQcUpdateService;

    /**
     * 商品详情缓存过期时间：24小时
     */
    private static final int PRODUCT_DETAIL_CACHE_EXPIRE_TIME = 24 * 60;
    
    /**
     * 空数据缓存过期时间：10分钟
     * 对于空数据采用短期缓存策略，避免频繁查询
     */
    private static final int EMPTY_DATA_CACHE_EXPIRE_TIME = 10;

    /**
     * 商品详情缓存根目录
     */
    private static final String PRODUCT_CACHE_ROOT = "omg_products";
    
    /**
     * 商品详情缓存子目录
     */
    private static final String PRODUCT_DETAIL_CACHE_DIR = "detail";

    /**
     * 获取商品详情缓存key
     * 统一存储在 omg_products:detail: 目录下
     * 
     * @param sku 商品SKU
     * @param platform 平台名称
     * @return 缓存key
     */
    private String getProductDetailCacheKey(String sku, String platform) {
        return PRODUCT_CACHE_ROOT + ":" + PRODUCT_DETAIL_CACHE_DIR + ":" + sku + ":" + getPlatformEnglishName(platform);
    }

    /**
     * 将平台中文名称转换为英文名称
     * 
     * @param platform 平台名称
     * @return 英文平台名称
     */
    private String getPlatformEnglishName(String platform) {
        if (platform == null || platform.isEmpty()) {
            return "unknown";
        }
        
        switch (platform.toLowerCase()) {
            case "淘宝":
                return "taobao";
            case "微店":
                return "weidian";
            case "1688":
                return "1688";
            case "天猫":
                return "tmall";
            case "京东":
                return "jd";
            case "拼多多":
                return "pdd";
            default:
                return platform.toLowerCase();
        }
    }

    /**
     * 从QC API返回结果中提取QC图片数量
     * 
     * @param qcImagesResult QC API返回的结果对象
     * @return QC图片数量
     */
    private int extractQcImageCount(Object qcImagesResult) {
        int qcImageCount = 0;
        
        // 处理返回结果
        if (qcImagesResult instanceof Map) {
            Map<String, Object> resultMap = (Map<String, Object>) qcImagesResult;
            
            // 从返回结果中提取图片数量
            if (resultMap.containsKey("total")) {
                Object totalObj = resultMap.get("total");
                if (totalObj instanceof Integer) {
                    qcImageCount = (Integer) totalObj;
                } else if (totalObj instanceof Number) {
                    qcImageCount = ((Number) totalObj).intValue();
                }
            } else if (resultMap.containsKey("images") && resultMap.get("images") instanceof List) {
                qcImageCount = ((List<?>) resultMap.get("images")).size();
            } else if (resultMap.containsKey("productImages") && resultMap.get("productImages") instanceof List) {
                qcImageCount = ((List<?>) resultMap.get("productImages")).size();
            }
        }
        
        return qcImageCount;
    }

    /**
     * @author: 小许
     * @date: 2025/5/15/周四 16:55
     * @description: 关键词搜索商品
     * @param oProductsVo
     * @return List<OProducts>
     */
    @Override
    public List<OProducts> searchProducts(OProductsVO oProductsVo) {
        try {
            // 创建一个默认的空字符串，防止keyword为null时发生异常
            String safeKeyword = oProductsVo.getKeyword() == null ? "" : oProductsVo.getKeyword();
            String safeCategoryId = oProductsVo.getCategoryId() == null ? "" : oProductsVo.getCategoryId().toString();
            List<OProducts> productsList = oProductsMapper.searchProducts(safeKeyword, safeCategoryId, oProductsVo.getMinPrice(), oProductsVo.getMaxPrice());
            for (OProducts products : productsList) {
                if (products.getMainImage() == null || products.getMainImage().equals("")) {
                    ProductMainImages productMainImages = productMainImagesMapper.selectMainImageBySku(products.getSku());
                    if (productMainImages == null || productMainImages.getOssImageUrl() == null || productMainImages.getOssImageUrl().equals("")) {
                        products.setMainImage("https://pic.616pic.com/ys_img/00/58/23/nid6gXk3tL.jpg");
                    }else {
                        products.setMainImage(productMainImages.getOssImageUrl());
                    }
                }
            }
            if (productsList == null) {
                throw new RuntimeException("搜索商品失败");
            }
            return productsList;
        } catch (RuntimeException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * @author: 小许
     * @date: 2025/5/15/周四 19:01
     * @description: 根据分类id和品牌id查询商品
     * @param categoryId
     * @param brandId
     * @return List<Products>
     */
    @Override
    public List<OProducts> getProductsByCategory(Long categoryId, Long brandId,String merchant) {
        try {
            List<OProducts> productsList = oProductsMapper.getProductsByCategory(categoryId, brandId, merchant);

            // 查询每个商品的QC图片
            for (OProducts product : productsList) {
                try {
                    List<OProductImages> qcImages = oProductImagesMapper.selectQcImagesByProductId(product.getProductId());
                    product.setQcImages(qcImages);
                    log.debug("分类商品ID {} 查询到 {} 张QC图片", product.getProductId(), qcImages.size());
                } catch (Exception e) {
                    log.error("查询分类商品ID为 {} 的QC图片时发生错误", product.getProductId(), e);
                }
            }
            return productsList;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public OProductsVO getProductById(Long id) {
        try {
            // 先查询商品基本信息，用于获取SKU和平台信息
            OProductsVO basicProductInfo = oProductsMapper.getProductById(id);
            if (basicProductInfo == null) {
                throw new RuntimeException("商品不存在");
            }

            // 生成缓存key
            String cacheKey = getProductDetailCacheKey(basicProductInfo.getSku(), basicProductInfo.getPlatform());
            log.info("查询商品详情，缓存Key: {}", cacheKey);

            // 1. 先从Redis缓存查询
            OProductsVO cachedProductsVo = redisCache.getCacheObject(cacheKey);
            if (cachedProductsVo != null) {
                // 检查是否是空数据占位符
                if ("empty_data".equals(cachedProductsVo.getRemark())) {
                    log.info("Redis缓存中的商品为空数据占位符，SKU: {}, 平台: {}", basicProductInfo.getSku(), basicProductInfo.getPlatform());
                    return null; // 返回null表示数据不存在，避免返回空占位符给客户端
                }
                
                // 检查是否是不完整数据
                if ("incomplete_data".equals(cachedProductsVo.getRemark())) {
                    log.info("Redis缓存中的商品数据不完整，SKU: {}, 平台: {}", basicProductInfo.getSku(), basicProductInfo.getPlatform());
                    // 这里可以选择返回不完整数据，也可以重新查询
                    // return cachedProductsVo; // 返回不完整数据
                    
                    // 重新查询以获取完整数据
                    log.info("尝试重新加载完整商品数据，SKU: {}, 平台: {}", basicProductInfo.getSku(), basicProductInfo.getPlatform());
                    // 不直接返回，继续执行下面的查询逻辑
                } else {
                    log.info("从Redis缓存获取完整商品详情成功，SKU: {}, 平台: {}", basicProductInfo.getSku(), basicProductInfo.getPlatform());
                    return cachedProductsVo;
                }
            }

            log.info("Redis缓存未命中，从数据库查询商品详情，SKU: {}", basicProductInfo.getSku());

            // 2. 缓存未命中，从数据库查询完整信息
            OProductsVO productsVo = loadProductDetailFromDatabase(id, basicProductInfo);

            // 3. 存储到Redis缓存，根据数据完整性采取不同的缓存策略
            if (productsVo != null) {
                try {
                    // 检查商品数据的完整性
                    boolean isDataComplete = checkProductDataCompleteness(productsVo);
                    
                    if (isDataComplete) {
                        // 完整数据使用正常的过期时间
                        redisCache.setCacheObject(cacheKey, productsVo, PRODUCT_DETAIL_CACHE_EXPIRE_TIME, TimeUnit.MINUTES);
                        log.info("完整商品详情已缓存到Redis，SKU: {}, 平台: {}, 过期时间: {}分钟",
                                basicProductInfo.getSku(), basicProductInfo.getPlatform(), PRODUCT_DETAIL_CACHE_EXPIRE_TIME);
                    } else {
                        // 对于不完整数据，设置特殊标记并使用短期缓存
                        productsVo.setRemark("incomplete_data"); // 添加标记，表示数据不完整
                        redisCache.setCacheObject(cacheKey, productsVo, EMPTY_DATA_CACHE_EXPIRE_TIME, TimeUnit.MINUTES);
                        log.warn("不完整商品详情已短期缓存，SKU: {}, 平台: {}, 过期时间: {}分钟",
                                basicProductInfo.getSku(), basicProductInfo.getPlatform(), EMPTY_DATA_CACHE_EXPIRE_TIME);
                    }
                } catch (Exception e) {
                    log.error("缓存商品详情到Redis失败，SKU: {}, 错误: {}", basicProductInfo.getSku(), e.getMessage(), e);
                }
            } else {
                // 处理空数据情况，缓存一个带标记的空对象，避免频繁查询
                try {
                    OProductsVO emptyProduct = new OProductsVO();
                    emptyProduct.setSku(basicProductInfo.getSku());
                    emptyProduct.setPlatform(basicProductInfo.getPlatform());
                    emptyProduct.setRemark("empty_data"); // 添加标记，表示空数据
                    
                    redisCache.setCacheObject(cacheKey, emptyProduct, EMPTY_DATA_CACHE_EXPIRE_TIME, TimeUnit.MINUTES);
                    log.warn("商品数据为空，已缓存空占位符，SKU: {}, 平台: {}, 过期时间: {}分钟", basicProductInfo.getSku(), basicProductInfo.getPlatform(), EMPTY_DATA_CACHE_EXPIRE_TIME);
                } catch (Exception e) {
                    log.error("缓存空商品占位符失败，SKU: {}, 错误: {}", basicProductInfo.getSku(), e.getMessage(), e);
                }
            }

            return productsVo;
        } catch (RuntimeException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 从数据库加载商品详情信息
     * 
     * @param id 商品ID
     * @param basicProductInfo 基本商品信息
     * @return 完整的商品详情信息
     */
    private OProductsVO loadProductDetailFromDatabase(Long id, OProductsVO basicProductInfo) {
        try {
            OProductsVO productsVo = basicProductInfo;

            // 查询商品评论
            List<OComments> commentsList = commentsMapper.getCommentsByProductId(id);
            productsVo.setComments(commentsList);
            for (OComments comments : commentsList) {
                Users users = usersMapper.selectById(comments.getUserId());
                if (users == null) {
                    throw new RuntimeException("用户不存在");
                }
                comments.setUsername(users.getUsername());
                comments.setAvatarUrl(users.getAvatarUrl());
            }

            // 获取所有商品图片
            List<ProductMainImagesVO> mainImages = productMainImagesMapper.selectVOByProductSku(productsVo.getSku());
            log.info("查询到所有商品主图数量: {}", mainImages.size());
            productsVo.setMainImages(mainImages);
            
            //查询相同sku的商品
            List<OSkuProductsVO> skuProductsList = oProductsMapper.getSkuProducts(productsVo.getSku());
            if (skuProductsList == null) {
                return null;
            }
            productsVo.setSameSkuProducts(skuProductsList);

            // 查询商品QC图片
            List<OProductImages> qcImages = oProductImagesMapper.selectQcImagesByProductId(id);

            try {
                //通过获取更多QC图片
                Object images = goodsController.getQcImages(productsVo.getSku());

                // 处理从QC API获取的图片数据
                if (images instanceof Map) {
                    Map<String, Object> resultMap = (Map<String, Object>) images;

                    // 检查是否成功获取到图片
                    if (Boolean.TRUE.equals(resultMap.get("success")) && resultMap.get("images") instanceof List) {
                        List<String> imageUrls = (List<String>) resultMap.get("images");
                        log.info("从QC API获取到{}张图片", imageUrls.size());

                        // 将图片URL转换为OProductImages对象并添加到qcImages集合
                        for (String imageUrl : imageUrls) {
                            OProductImages productImages = new OProductImages();
                            productImages.setImageUrl(imageUrl);
                            productImages.setCreatedAt(new Timestamp(System.currentTimeMillis()));
                            qcImages.add(productImages);
                        }
                    } else if (resultMap.get("productImages") instanceof List) {
                        // 如果返回的是ProductImages对象列表
                        List<ProductImages> productImagesList = (List<ProductImages>) resultMap.get("productImages");
                        log.info("从QC API获取到{}个ProductImages对象", productImagesList.size());

                        // 转换为OProductImages对象并添加到qcImages集合
                        for (ProductImages productImage : productImagesList) {
                            OProductImages oProductImage = new OProductImages();
                            oProductImage.setImageUrl(productImage.getImageUrl());
                            oProductImage.setCreatedAt(productImage.getCreatedAt());
                            qcImages.add(oProductImage);
                        }
                    } else {
                        log.warn("QC API未返回有效图片数据");
                    }
                } else {
                    log.warn("QC API返回的数据格式不是Map类型");
                }

                // 使用OkHttp实现替代RestTemplate
                GoodsDetail goodsDetail = okHttpGoodsService.getGoodsDetail(productsVo.getSku(), "weidian", "en", "USD");

                goodsDetail.getQcList().forEach(qcItem -> {
                    OProductImages productImages = new OProductImages();
                    productImages.setImageUrl(qcItem.getUrl());
                    //时间戳转为日期类型
                    productImages.setCreatedAt(new Timestamp(qcItem.getTime()));
                    qcImages.add(productImages);
                });

            } catch (Exception e) {
                // 记录异常，但不中断流程
                log.error("获取QC图片时发生异常: {}", e.getMessage(), e);
            }
            productsVo.setQcImages(qcImages);

            return productsVo;
        } catch (Exception e) {
            log.error("从数据库加载商品详情失败，商品ID: {}", id, e);
            throw new RuntimeException("加载商品详情失败", e);
        }
    }

    @Override
    public int updateViews(Long productId) {
        try {
            // 先查询商品信息，用于更新缓存
            OProductsVO product = oProductsMapper.getProductById(productId);
            
            // 更新浏览量
            int result = oProductsMapper.updateViews(productId);
            
            // 更新成功后使用智能缓存更新策略
            if (result > 0 && product != null) {
                // 创建包含更新后浏览量的商品对象
                OProducts oProduct = new OProducts();
                oProduct.setSku(product.getSku());
                oProduct.setPlatform(product.getPlatform());
                oProduct.setViews(product.getViews() != null ? product.getViews() + 1 : 1); // 浏览量+1

                // 使用统计数据更新策略，更新缓存而不是删除
                smartCacheUpdate(oProduct, CacheUpdateType.STATS_DATA);
                log.info("更新商品浏览量后更新缓存统计数据，商品ID: {}, SKU: {}", productId, product.getSku());
            }
            
            return result;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<OProducts> getRecommendProducts(Products products) {
        try {
            List<OProducts> recommendProducts = oProductsMapper.getRecommendProducts(products.getCategoryId(),products.getBrandId());

            if (products == null) {
                throw ExceptionUtil.wrapRuntime("商品不能为空");
            }

            // 查询每个商品的QC图片
            for (OProducts product : recommendProducts) {
                try {
                    List<OProductImages> qcImages = oProductImagesMapper.selectQcImagesByProductId(product.getProductId());
                    product.setQcImages(qcImages);
                    //获取商品主图第一张
                    ProductMainImages mainImages = productMainImagesMapper.selectMainImageBySku(product.getSku());
                    product.setMainImage(mainImages.getOssImageUrl());

                } catch (Exception e) {
                    log.error("查询推荐商品ID为 {} 的QC图片时发生错误", product.getProductId(), e);
                }
            }

            return recommendProducts;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    /**
     * 批量更新所有商品的QC图片数量（优化版 - 使用异步处理）
     *
     * @return 更新成功的商品数量
     */
    public int batchUpdateProductsQcCount() {
        try {
            // 获取所有商品
            List<OProducts> allProducts = oProductsMapper.selectAllProductsBasicInfo();
            log.info("开始异步批量更新{}个商品的QC图片数量", allProducts.size());

            // 使用异步服务处理
            asyncQcUpdateService.asyncBatchUpdateProductsQc(allProducts);

            // 立即返回，实际处理在后台进行
            log.info("已提交异步批量更新任务，商品数量: {}", allProducts.size());
            return allProducts.size(); // 返回提交的商品数量

        } catch (Exception e) {
            log.error("提交异步批量更新商品QC图片数量任务时发生异常: {}", e.getMessage(), e);
            throw new RuntimeException("提交批量更新任务失败", e);
        }
    }

    /**
     * 分页批量更新商品的QC图片数量（优化版 - 使用异步处理）
     *
     * @param pageSize 每页处理的商品数量
     * @return 更新成功的商品数量
     */
    public int batchUpdateProductsQcCountWithPaging(int pageSize) {
        try {
            // 获取商品总数
            long totalCount = this.count();
            log.info("开始异步分页批量更新{}个商品的QC图片数量，每页{}条", totalCount, pageSize);

            // 使用异步服务处理
            asyncQcUpdateService.asyncBatchUpdateProductsQcWithPaging(pageSize);

            // 立即返回，实际处理在后台进行
            log.info("已提交异步分页批量更新任务，总商品数量: {}, 每页: {}", totalCount, pageSize);
            return (int) totalCount; // 返回总商品数量

        } catch (Exception e) {
            log.error("提交异步分页批量更新商品QC图片数量任务时发生异常: {}", e.getMessage(), e);
            throw new RuntimeException("提交分页批量更新任务失败", e);
        }
    }

    /**
     * 根据SKU列表批量更新商品的QC图片数量（优化版 - 使用异步处理）
     *
     * @param skuList 要更新的SKU列表
     * @return 更新成功的商品数量
     */
    public int batchUpdateProductsQcCountBySku(List<String> skuList) {
        try {
            log.info("开始异步根据SKU列表批量更新{}个商品的QC图片数量", skuList.size());

            // 使用异步服务处理
            asyncQcUpdateService.asyncBatchUpdateProductsQcBySku(skuList);

            // 立即返回，实际处理在后台进行
            log.info("已提交异步SKU批量更新任务，SKU数量: {}", skuList.size());
            return skuList.size(); // 返回提交的SKU数量

        } catch (Exception e) {
            log.error("提交异步SKU批量更新商品QC图片数量任务时发生异常: {}", e.getMessage(), e);
            throw new RuntimeException("提交SKU批量更新任务失败", e);
        }
    }

    @Override
    public List<String> getAllMerchant() {
        try {
            return oProductsMapper.getAllMerchant();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    
    /**
     * 获取指定product_status的产品列表
     * 
     * @param statusList 产品状态值列表
     * @return 符合条件的产品列表
     */
    @Override
    public List<OProducts> getProductsByStatus(List<Integer> statusList) {
        try {
            if (statusList == null || statusList.isEmpty()) {
                return Collections.emptyList();
            }
            
            List<OProducts> productsList = oProductsMapper.getProductsByStatus(statusList);
            
            // 查询每个商品的QC图片
            for (OProducts product : productsList) {
                try {
                    List<OProductImages> qcImages = oProductImagesMapper.selectQcImagesByProductId(product.getProductId());
                    product.setQcImages(qcImages);
                    log.debug("商品ID {} 查询到 {} 张QC图片", product.getProductId(), qcImages.size());
                } catch (Exception e) {
                    log.error("查询商品ID为 {} 的QC图片时发生错误", product.getProductId(), e);
                }
            }
            
            return productsList;
        } catch (Exception e) {
            log.error("获取指定状态商品列表时发生错误", e);
            throw new RuntimeException("获取商品列表失败", e);
        }
    }

    @Override
    public boolean clearProductDetailCache(String sku, String platform) {
        try {
            String cacheKey = getProductDetailCacheKey(sku, platform);
            boolean deleted = redisCache.deleteObject(cacheKey);
            if (deleted) {
                log.info("成功清除商品详情缓存，SKU: {}, 平台: {}", sku, platform);
            } else {
                log.warn("清除商品详情缓存失败或缓存不存在，SKU: {}, 平台: {}", sku, platform);
            }
            return deleted;
        } catch (Exception e) {
            log.error("清除商品详情缓存时发生异常，SKU: {}, 平台: {}, 错误: {}", sku, platform, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 在更新商品数据后清除对应的缓存
     * 此方法应在任何修改商品数据的方法中调用
     * 
     * @param product 更新的商品对象
     */
    private void clearCacheAfterUpdate(OProducts product) {
        if (product != null && product.getSku() != null && product.getPlatform() != null) {
            try {
                String cacheKey = getProductDetailCacheKey(product.getSku(), product.getPlatform());
                boolean deleted = redisCache.deleteObject(cacheKey);
                if (deleted) {
                    log.info("更新商品后成功清除缓存，SKU: {}, 平台: {}", product.getSku(), product.getPlatform());
                } else {
                    log.debug("更新商品后清除缓存不存在或失败，SKU: {}, 平台: {}", product.getSku(), product.getPlatform());
                }
            } catch (Exception e) {
                log.error("更新商品后清除缓存时发生异常，SKU: {}, 平台: {}, 错误: {}",
                        product.getSku(), product.getPlatform(), e.getMessage(), e);
            }
        }
    }

    @Override
    public boolean hasProductDetailCache(String sku, String platform) {
        try {
            String cacheKey = getProductDetailCacheKey(sku, platform);
            return redisCache.hasKey(cacheKey);
        } catch (Exception e) {
            log.error("检查商品详情缓存时发生异常，SKU: {}, 平台: {}, 错误: {}", sku, platform, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public int clearAllProductDetailCache() {
        try {
            // 构建缓存目录模式
            String cachePattern = PRODUCT_CACHE_ROOT + ":" + PRODUCT_DETAIL_CACHE_DIR + ":*";
            
            // 查找所有匹配的缓存key
            var keys = redisCache.keys(cachePattern);
            
            if (keys != null && !keys.isEmpty()) {
                // 批量删除
                redisCache.deleteObject(keys);
                log.info("成功清除所有商品详情缓存，共清除 {} 个缓存", keys.size());
                return keys.size();
            } else {
                log.info("没有找到需要清除的商品详情缓存");
                return 0;
            }
        } catch (Exception e) {
            log.error("清除所有商品详情缓存时发生异常，错误: {}", e.getMessage(), e);
            return -1;
        }
    }

    @Override
    public List<OProducts> getAllProductsCount() {
        try {
            return baseMapper.selectAllProductsBasicInfo();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public Object getQcUpdateTaskStatus(String taskId) {
        try {
            return asyncQcUpdateService.getTaskStatus(taskId);
        } catch (Exception e) {
            log.error("获取QC更新任务状态失败，任务ID: {}", taskId, e);
            throw new RuntimeException("获取任务状态失败", e);
        }
    }

    /**
     * 检查商品数据的完整性
     * 
     * @param product 商品数据
     * @return 数据是否完整
     */
    private boolean checkProductDataCompleteness(OProductsVO product) {
        if (product == null) {
            return false;
        }
        
        // 检查基本信息是否完整
        if (product.getName() == null || product.getName().isEmpty() ||
            product.getSku() == null || product.getSku().isEmpty() ||
            product.getMainImage() == null || product.getMainImage().isEmpty()) {
            log.debug("商品基本信息不完整，SKU: {}", product.getSku());
            return false;
        }
        
        // 检查QC图片是否加载
        List<OProductImages> qcImages = product.getQcImages();
        if (qcImages == null || qcImages.isEmpty()) {
            log.debug("商品QC图片为空，SKU: {}", product.getSku());
            // 这里可以根据业务需求决定是否因为QC图片为空就判定为数据不完整
            // 如果QC图片不是必须的，可以注释这个return
            // return false;
        }
        
        // 检查SKU商品列表是否加载
        List<OSkuProductsVO> skuProducts = product.getSameSkuProducts();
        if (skuProducts == null || skuProducts.isEmpty()) {
            log.debug("商品SKU列表为空，SKU: {}", product.getSku());
            return false;
        }
        
        // 检查评论数据是否加载 (这里不检查评论为空的情况，因为可能就是没有评论)
        if (product.getComments() == null) {
            log.debug("商品评论列表为null，SKU: {}", product.getSku());
            return false;
        }
        
        // 所有必要数据都已加载
        return true;
    }

    /**
     * 智能缓存更新策略
     * 根据更新的数据类型选择合适的缓存处理方式
     *
     * @param product 更新的商品对象
     * @param updateType 更新类型：CORE_DATA(核心数据), STATS_DATA(统计数据), QC_DATA(QC数据)
     */
    private void smartCacheUpdate(OProducts product, CacheUpdateType updateType) {
        if (product == null || product.getSku() == null || product.getPlatform() == null) {
            return;
        }

        try {
            switch (updateType) {
                case CORE_DATA:
                    // 核心数据变更：清除详情缓存，因为基本信息已改变
                    clearProductDetailCache(product.getSku(), product.getPlatform());
                    log.info("核心数据更新，已清除商品详情缓存，SKU: {}, 平台: {}", product.getSku(), product.getPlatform());
                    break;

                case STATS_DATA:
                    // 统计数据变更：更新缓存中的统计数据，而不是删除整个缓存
                    updateProductStatsInCache(product);
                    log.info("统计数据更新，已更新缓存中的统计信息，SKU: {}, 平台: {}", product.getSku(), product.getPlatform());
                    break;

                case QC_DATA:
                    // QC数据变更：只更新QC相关的缓存数据
                    updateProductQcDataInCache(product);
                    log.info("QC数据更新，已更新缓存中的QC信息，SKU: {}, 平台: {}", product.getSku(), product.getPlatform());
                    break;

                default:
                    // 未知类型：保守策略，清除缓存
                    clearProductDetailCache(product.getSku(), product.getPlatform());
                    log.warn("未知更新类型，已清除商品详情缓存，SKU: {}, 平台: {}", product.getSku(), product.getPlatform());
                    break;
            }
        } catch (Exception e) {
            log.error("智能缓存更新时发生异常，SKU: {}, 平台: {}, 更新类型: {}, 错误: {}",
                    product.getSku(), product.getPlatform(), updateType, e.getMessage(), e);
        }
    }

    /**
     * 更新缓存中的统计数据
     *
     * @param product 商品对象
     */
    private void updateProductStatsInCache(OProducts product) {
        try {
            String cacheKey = getProductDetailCacheKey(product.getSku(), product.getPlatform());

            // 获取当前缓存的商品详情
            OProductsVO cachedProduct = redisCache.getCacheObject(cacheKey);
            if (cachedProduct != null) {
                // 更新统计数据
                boolean statsUpdated = false;

                if (product.getViews() != null) {
                    cachedProduct.setViews(product.getViews());
                    statsUpdated = true;
                }

                if (product.getLikes() != null) {
                    cachedProduct.setLikes(product.getLikes());
                    statsUpdated = true;
                }

                // 只有在实际更新了统计数据时才重新存储缓存
                if (statsUpdated) {
                    // 重新存储到缓存，保持原有的过期时间策略
                    boolean isDataComplete = checkProductDataCompleteness(cachedProduct);
                    int expireTime = isDataComplete ? PRODUCT_DETAIL_CACHE_EXPIRE_TIME : EMPTY_DATA_CACHE_EXPIRE_TIME;

                    redisCache.setCacheObject(cacheKey, cachedProduct, expireTime, TimeUnit.MINUTES);
                    log.debug("已更新缓存中的统计数据，SKU: {}, 平台: {}", product.getSku(), product.getPlatform());
                }
            } else {
                log.debug("缓存中不存在商品数据，跳过统计数据更新，SKU: {}, 平台: {}", product.getSku(), product.getPlatform());
            }
        } catch (Exception e) {
            log.error("更新缓存中的统计数据时发生异常，SKU: {}, 平台: {}, 错误: {}",
                    product.getSku(), product.getPlatform(), e.getMessage(), e);
        }
    }

    /**
     * 更新缓存中的QC数据
     *
     * @param product 商品对象
     */
    private void updateProductQcDataInCache(OProducts product) {
        try {
            String cacheKey = getProductDetailCacheKey(product.getSku(), product.getPlatform());

            // 获取当前缓存的商品详情
            OProductsVO cachedProduct = redisCache.getCacheObject(cacheKey);
            if (cachedProduct != null) {
                // 更新QC数据
                if (product.getQc() != null) {
                    cachedProduct.setQc(product.getQc());
                }

                // 如果需要更新QC图片，重新查询
                if (product.getProductId() != null) {
                    try {
                        List<OProductImages> qcImages = oProductImagesMapper.selectQcImagesByProductId(product.getProductId());
                        cachedProduct.setQcImages(qcImages);
                    } catch (Exception e) {
                        log.warn("更新缓存QC数据时，重新查询QC图片失败，商品ID: {}", product.getProductId(), e);
                    }
                }

                // 重新存储到缓存
                boolean isDataComplete = checkProductDataCompleteness(cachedProduct);
                int expireTime = isDataComplete ? PRODUCT_DETAIL_CACHE_EXPIRE_TIME : EMPTY_DATA_CACHE_EXPIRE_TIME;

                redisCache.setCacheObject(cacheKey, cachedProduct, expireTime, TimeUnit.MINUTES);
                log.debug("已更新缓存中的QC数据，SKU: {}, 平台: {}", product.getSku(), product.getPlatform());
            } else {
                log.debug("缓存中不存在商品数据，跳过QC数据更新，SKU: {}, 平台: {}", product.getSku(), product.getPlatform());
            }
        } catch (Exception e) {
            log.error("更新缓存中的QC数据时发生异常，SKU: {}, 平台: {}, 错误: {}",
                    product.getSku(), product.getPlatform(), e.getMessage(), e);
        }
    }
}
