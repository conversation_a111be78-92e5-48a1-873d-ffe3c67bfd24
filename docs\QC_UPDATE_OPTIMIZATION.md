# QC更新优化方案

## 概述

本文档描述了对商品QC图片数量批量更新功能的两个主要优化：
1. **批量操作优化**：使用批量SQL更新替代逐个更新
2. **异步处理优化**：将耗时操作异步化，提升用户体验

## 1. 批量操作优化

### 优化前问题
- 逐个调用 `updateById()` 方法，每次更新都是一个独立的数据库事务
- 大量商品更新时性能低下
- 数据库连接资源消耗大

### 优化后方案
- 使用批量SQL更新，一次SQL语句更新多个商品
- 减少数据库交互次数
- 提升更新性能

### 新增方法

#### Mapper层
```java
// OProductsMapper.java
int batchUpdateProductsQc(@Param("list") List<OProducts> productList);
int batchUpdateProductsQcBySku(@Param("skuQcMap") Map<String, String> skuQcMap);
```

#### XML实现
```xml
<!-- 批量更新商品QC数量 -->
<update id="batchUpdateProductsQc" parameterType="java.util.List">
    <foreach collection="list" item="item" separator=";">
        UPDATE omg_products 
        SET qc = #{item.qc}, updated_at = NOW()
        WHERE product_id = #{item.productId}
    </foreach>
</update>

<!-- 根据SKU批量更新商品QC数量 -->
<update id="batchUpdateProductsQcBySku" parameterType="java.util.Map">
    <foreach collection="skuQcMap.entrySet()" item="value" index="key" separator=";">
        UPDATE omg_products 
        SET qc = #{value}, updated_at = NOW()
        WHERE sku = #{key}
    </foreach>
</update>
```

## 2. 异步处理优化

### 优化前问题
- QC图片数量更新是同步操作，用户需要等待完成
- 大量商品更新时接口响应时间长
- 容易导致接口超时

### 优化后方案
- 使用Spring的 `@Async` 注解实现异步处理
- 立即返回任务提交结果，后台执行实际更新
- 提供任务状态查询接口

### 核心组件

#### 1. AsyncQcUpdateService 接口
```java
public interface AsyncQcUpdateService {
    CompletableFuture<Integer> asyncBatchUpdateProductsQc(List<OProducts> productList);
    CompletableFuture<Integer> asyncBatchUpdateProductsQcWithPaging(int pageSize);
    CompletableFuture<Integer> asyncBatchUpdateProductsQcBySku(List<String> skuList);
    TaskStatus getTaskStatus(String taskId);
}
```

#### 2. AsyncQcUpdateServiceImpl 实现类
- 使用 `@Async("taskExecutor")` 注解
- 批量处理优化（每批50个商品）
- 任务状态跟踪
- 错误处理和重试机制

#### 3. AsyncQcUpdateController 控制器
提供以下API接口：
- `POST /api/async-qc/batch-update-all` - 异步批量更新所有商品
- `POST /api/async-qc/batch-update-paging` - 异步分页批量更新
- `POST /api/async-qc/batch-update-by-sku` - 异步根据SKU列表更新
- `GET /api/async-qc/task-status/{taskId}` - 查询任务状态

## 3. 使用方式

### 3.1 启动异步批量更新
```bash
# 更新所有商品
curl -X POST http://localhost:8080/api/async-qc/batch-update-all

# 分页更新（每页100个商品）
curl -X POST "http://localhost:8080/api/async-qc/batch-update-paging?pageSize=100"

# 根据SKU列表更新
curl -X POST http://localhost:8080/api/async-qc/batch-update-by-sku \
  -H "Content-Type: application/json" \
  -d '["SKU001", "SKU002", "SKU003"]'
```

### 3.2 查询任务状态
```bash
curl -X GET http://localhost:8080/api/async-qc/task-status/batch_qc_update_1691234567890
```

响应示例：
```json
{
  "success": true,
  "taskId": "batch_qc_update_1691234567890",
  "status": "RUNNING",
  "progress": 65,
  "message": "正在批量更新QC图片数量...",
  "startTime": 1691234567890,
  "endTime": 0,
  "duration": 45000,
  "successCount": 650,
  "errorCount": 5
}
```

## 4. 配置参数

### application-async-qc.yml
```yaml
async:
  qc-update:
    batch-size: 50              # 批处理大小
    request-delay: 200          # API请求延迟（毫秒）
    task-status-expire-hours: 24 # 任务状态缓存过期时间
    enable-batch-update: true   # 启用批量数据库更新
    timeout-seconds: 3600       # 超时时间
    retry-count: 3              # 重试次数
    retry-delay: 5000           # 重试延迟
```

## 5. 性能对比

### 优化前
- 1000个商品更新：约20-30分钟
- 数据库连接数：1000次独立连接
- 用户体验：需要等待完成

### 优化后
- 1000个商品更新：约5-10分钟（后台执行）
- 数据库连接数：20次批量连接（每批50个）
- 用户体验：立即返回，可查询进度

## 6. 注意事项

1. **数据库配置**：确保数据库支持批量更新操作
2. **线程池配置**：根据服务器性能调整线程池大小
3. **API限流**：注意QC图片API的调用频率限制
4. **错误处理**：异步任务中的错误需要通过任务状态查询获取
5. **缓存清理**：任务状态缓存会定期清理，避免内存泄漏

## 7. 监控和日志

- 所有异步任务都有详细的日志记录
- 可以通过任务状态接口监控执行进度
- 建议配置日志告警，及时发现异常情况

## 8. 扩展性

该优化方案具有良好的扩展性：
- 可以轻松添加新的批量更新类型
- 支持不同的批处理策略
- 可以集成更多的监控和告警功能
