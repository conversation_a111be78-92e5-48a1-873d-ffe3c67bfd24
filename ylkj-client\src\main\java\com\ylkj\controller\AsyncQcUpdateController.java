package com.ylkj.controller;

import com.ylkj.service.omg.AsyncQcUpdateService;
import com.ylkj.service.omg.OIProductsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 异步QC更新控制器
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@Slf4j
@RestController
@RequestMapping("/front/async-qc")
@Api(tags = "异步QC更新管理")
public class AsyncQcUpdateController {
    
    @Autowired
    private OIProductsService oProductsService;
    
    @Autowired
    private AsyncQcUpdateService asyncQcUpdateService;
    
    /**
     * 异步批量更新所有商品QC图片数量
     */
    @PostMapping("/batch-update-all")
    @ApiOperation("异步批量更新所有商品QC图片数量")
    public ResponseEntity<Map<String, Object>> batchUpdateAllProductsQc() {
        try {
            int submittedCount = oProductsService.batchUpdateProductsQcCount();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "异步批量更新任务已提交");
            response.put("submittedCount", submittedCount);
            response.put("note", "任务正在后台执行，请使用任务状态查询接口获取进度");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("提交异步批量更新任务失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "提交任务失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 异步分页批量更新商品QC图片数量
     */
    @PostMapping("/batch-update-paging")
    @ApiOperation("异步分页批量更新商品QC图片数量")
    public ResponseEntity<Map<String, Object>> batchUpdateProductsQcWithPaging(
            @ApiParam(value = "每页处理的商品数量", example = "100") 
            @RequestParam(defaultValue = "100") int pageSize) {
        try {
            int totalCount = oProductsService.batchUpdateProductsQcCountWithPaging(pageSize);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "异步分页批量更新任务已提交");
            response.put("totalCount", totalCount);
            response.put("pageSize", pageSize);
            response.put("note", "任务正在后台执行，请使用任务状态查询接口获取进度");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("提交异步分页批量更新任务失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "提交任务失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 异步根据SKU列表批量更新商品QC图片数量
     */
    @PostMapping("/batch-update-by-sku")
    @ApiOperation("异步根据SKU列表批量更新商品QC图片数量")
    public ResponseEntity<Map<String, Object>> batchUpdateProductsQcBySku(
            @ApiParam(value = "SKU列表", required = true) 
            @RequestBody List<String> skuList) {
        try {
            if (skuList == null || skuList.isEmpty()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "SKU列表不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            int submittedCount = oProductsService.batchUpdateProductsQcCountBySku(skuList);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "异步SKU批量更新任务已提交");
            response.put("submittedCount", submittedCount);
            response.put("note", "任务正在后台执行，请使用任务状态查询接口获取进度");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("提交异步SKU批量更新任务失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "提交任务失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 查询任务执行状态
     */
    @GetMapping("/task-status/{taskId}")
    @ApiOperation("查询异步任务执行状态")
    public ResponseEntity<Map<String, Object>> getTaskStatus(
            @ApiParam(value = "任务ID", required = true) 
            @PathVariable String taskId) {
        try {
            AsyncQcUpdateService.TaskStatus taskStatus = asyncQcUpdateService.getTaskStatus(taskId);
            
            Map<String, Object> response = new HashMap<>();
            if (taskStatus != null) {
                response.put("success", true);
                response.put("taskId", taskStatus.getTaskId());
                response.put("status", taskStatus.getStatus());
                response.put("progress", taskStatus.getProgress());
                response.put("message", taskStatus.getMessage());
                response.put("startTime", taskStatus.getStartTime());
                response.put("endTime", taskStatus.getEndTime());
                response.put("duration", taskStatus.getDuration());
                response.put("successCount", taskStatus.getSuccessCount());
                response.put("errorCount", taskStatus.getErrorCount());
                
                if (taskStatus.getLastError() != null) {
                    response.put("lastError", taskStatus.getLastError().getMessage());
                }
            } else {
                response.put("success", false);
                response.put("message", "任务不存在或已过期");
            }
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("查询任务状态失败，任务ID: {}", taskId, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "查询任务状态失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}
