package com.ylkj.system.service;

import com.ylkj.system.model.domain.OmgProducts;

import java.util.List;

/**
 * 异步主图更新服务接口
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
public interface IAsyncMainImageUpdateService {
    
    /**
     * 异步批量更新商品主图
     * 在商品导入完成后调用，异步处理主图获取和更新
     *
     * @param successfulProducts 成功导入的商品列表
     */
    void asyncBatchUpdateMainImages(List<OmgProducts> successfulProducts);
    
    /**
     * 异步更新单个商品主图
     * 
     * @param product 商品信息
     */
    void asyncUpdateSingleMainImage(OmgProducts product);
    
    /**
     * 根据平台名称获取对应的mallType
     * 
     * @param platform 平台名称
     * @return mallType
     */
    String getMallTypeByPlatform(String platform);
}
