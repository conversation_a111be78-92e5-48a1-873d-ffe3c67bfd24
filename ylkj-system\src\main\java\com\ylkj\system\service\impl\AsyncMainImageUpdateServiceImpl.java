package com.ylkj.system.service.impl;

import com.ylkj.system.mapper.OmgProductsMapper;
import com.ylkj.system.model.domain.OmgProducts;
import com.ylkj.system.model.result.MainImageProcessResult;
import com.ylkj.system.service.IAsyncMainImageUpdateService;
import com.ylkj.system.service.IMainImageProcessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 异步主图更新服务实现类
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
@Service
public class AsyncMainImageUpdateServiceImpl implements IAsyncMainImageUpdateService {

    @Resource
    private OmgProductsMapper omgProductsMapper;

    @Resource
    private IMainImageProcessService mainImageProcessService;

    @Value("${async.main-image.batch-size:10}")
    private int batchSize;

    @Value("${async.main-image.thread-pool-size:3}")
    private int threadPoolSize;

    @Value("${async.main-image.request-delay:1500}")
    private long requestDelay;

    /**
     * 异步批量更新商品主图
     * 在商品导入完成后调用，异步处理主图获取和更新
     *
     * @param successfulProducts 成功导入的商品列表
     */
    @Override
    @Async("taskExecutor")
    public void asyncBatchUpdateMainImages(List<OmgProducts> successfulProducts) {
        if (successfulProducts == null || successfulProducts.isEmpty()) {
            log.info("没有需要更新主图的商品");
            return;
        }

        log.info("开始异步批量更新商品主图，商品数量: {}", successfulProducts.size());
        long startTime = System.currentTimeMillis();

        // 预过滤有效商品
        List<OmgProducts> validProducts = preFilterValidProducts(successfulProducts);
        if (validProducts.isEmpty()) {
            log.info("没有有效的商品需要更新主图");
            return;
        }

        log.info("预过滤后有效商品数量: {}", validProducts.size());

        // 使用并行处理
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failedCount = new AtomicInteger(0);
        AtomicInteger skippedCount = new AtomicInteger(successfulProducts.size() - validProducts.size());

        // 分批并行处理
        processBatchesInParallel(validProducts, successCount, failedCount, skippedCount);

        long endTime = System.currentTimeMillis();
        log.info("异步批量更新商品主图完成，总数: {}, 成功: {}, 失败: {}, 跳过: {}, 耗时: {}ms",
                successfulProducts.size(), successCount.get(), failedCount.get(),
                skippedCount.get(), (endTime - startTime));
    }

    /**
     * 预过滤有效商品
     */
    private List<OmgProducts> preFilterValidProducts(List<OmgProducts> products) {
        return products.stream()
                .filter(product -> {
                    String sku = product.getSku();
                    String platform = product.getPlatform();

                    if (sku == null || sku.trim().isEmpty()) {
                        log.debug("商品SKU为空，跳过主图更新，商品ID: {}", product.getProductId());
                        return false;
                    }

                    if (platform == null || platform.trim().isEmpty()) {
                        log.debug("商品平台为空，跳过主图更新，SKU: {}", sku);
                        return false;
                    }

                    String mallType = getMallTypeByPlatform(platform);
                    if (mallType == null) {
                        log.debug("平台 {} 不支持主图处理，跳过主图更新，SKU: {}", platform, sku);
                        return false;
                    }

                    return true;
                })
                .collect(Collectors.toList());
    }

    /**
     * 分批并行处理商品主图更新
     */
    private void processBatchesInParallel(List<OmgProducts> validProducts,
                                        AtomicInteger successCount,
                                        AtomicInteger failedCount,
                                        AtomicInteger skippedCount) {

        // 创建专用线程池
        ExecutorService executor = Executors.newFixedThreadPool(threadPoolSize);

        try {
            // 分批处理
            List<List<OmgProducts>> batches = partitionList(validProducts, batchSize);
            List<CompletableFuture<Void>> futures = new ArrayList<>();

            for (int i = 0; i < batches.size(); i++) {
                final int batchIndex = i;
                final List<OmgProducts> batch = batches.get(i);

                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    processSingleBatch(batch, batchIndex, successCount, failedCount, skippedCount);
                }, executor);

                futures.add(future);
            }

            // 等待所有批次完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        } finally {
            executor.shutdown();
            try {
                if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 异步更新单个商品主图
     * 
     * @param product 商品信息
     */
    @Override
    @Async("taskExecutor")
    public void asyncUpdateSingleMainImage(OmgProducts product) {
        if (product == null) {
            log.warn("商品信息为空，跳过主图更新");
            return;
        }

        try {
            String sku = product.getSku();
            String platform = product.getPlatform();

            if (sku == null || sku.trim().isEmpty()) {
                log.warn("商品SKU为空，跳过主图更新，商品ID: {}", product.getProductId());
                return;
            }

            if (platform == null || platform.trim().isEmpty()) {
                log.warn("商品平台为空，跳过主图更新，SKU: {}", sku);
                return;
            }

            String mallType = getMallTypeByPlatform(platform);
            if (mallType == null) {
                log.warn("平台 {} 不支持主图处理，跳过主图更新，SKU: {}", platform, sku);
                return;
            }

           MainImageProcessResult processResult =
                    mainImageProcessService.processProductMainImage(product, mallType);

            if (processResult.isSuccess() && processResult.getMainImageUrl() != null) {
                product.setMainImage(processResult.getMainImageUrl());
                int updateResult = omgProductsMapper.updateOmgProducts(product);

                if (updateResult > 0) {
                    log.info("SKU: {} 主图更新成功，主图URL: {}", sku, processResult.getMainImageUrl());
                } else {
                    log.warn("SKU: {} 主图更新失败，数据库更新失败", sku);
                }
            } else {
                log.warn("SKU: {} 主图获取失败: {}", sku, processResult.getMessage());
            }

        } catch (Exception e) {
            log.error("处理单个商品主图异常，SKU: {}", product.getSku(), e);
        }
    }

    /**
     * 根据平台名称获取对应的mallType
     * 
     * @param platform 平台名称
     * @return mallType
     */
    @Override
    public String getMallTypeByPlatform(String platform) {
        if (platform == null) {
            return null;
        }
        switch (platform.toLowerCase()) {
            case "淘宝":
            case "taobao":
                return "TAOBAO";
            case "1688":
                return "T1688";
            case "weidian":
            case "微店":
                return "WEIDIAN";
            default:
                return null;
        }
    }

    /**
     * 处理单个批次
     */
    private void processSingleBatch(List<OmgProducts> batch, int batchIndex,
                                  AtomicInteger successCount,
                                  AtomicInteger failedCount,
                                  AtomicInteger skippedCount) {

        log.info("开始处理批次 {}, 商品数量: {}", batchIndex + 1, batch.size());

        for (OmgProducts product : batch) {
            try {
                String sku = product.getSku();
                String platform = product.getPlatform();
                String mallType = getMallTypeByPlatform(platform);

                // 调用主图处理逻辑
                MainImageProcessResult processResult =
                        mainImageProcessService.processProductMainImage(product, mallType);

                if (processResult.isSuccess() && processResult.getMainImageUrl() != null) {
                    // 更新商品主图
//                    product.setMainImage(processResult.getMainImageUrl());
                    int updateResult = omgProductsMapper.updateOmgProducts(product);

                    if (updateResult > 0) {
                        successCount.incrementAndGet();
                        log.debug("SKU: {} 主图更新成功，主图URL: {}", sku, processResult.getMainImageUrl());
                    } else {
                        failedCount.incrementAndGet();
                        log.warn("SKU: {} 主图更新失败，数据库更新失败", sku);
                    }
                } else {
                    failedCount.incrementAndGet();
                    log.warn("SKU: {} 主图获取失败: {}", sku, processResult.getMessage());
                }

                // 添加延迟避免请求过于频繁
                if (requestDelay > 0) {
                    Thread.sleep(requestDelay);
                }

            } catch (Exception e) {
                failedCount.incrementAndGet();
                log.error("处理商品主图异常，SKU: {}", product.getSku(), e);
            }
        }

        log.info("批次 {} 处理完成", batchIndex + 1);
    }

    /**
     * 将列表分割成指定大小的批次
     */
    private <T> List<List<T>> partitionList(List<T> list, int batchSize) {
        List<List<T>> partitions = new ArrayList<>();
        for (int i = 0; i < list.size(); i += batchSize) {
            partitions.add(list.subList(i, Math.min(i + batchSize, list.size())));
        }
        return partitions;
    }
}
