package com.ylkj.system.service.impl;

import com.ylkj.system.config.AsyncMainImageConfig;
import com.ylkj.system.mapper.OmgProductsMapper;
import com.ylkj.system.model.domain.OmgProducts;
import com.ylkj.system.model.result.MainImageProcessResult;
import com.ylkj.system.service.IAsyncMainImageUpdateService;
import com.ylkj.system.service.IMainImageProcessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 增强版异步主图更新服务实现
 * 支持并行处理、重试机制、缓存等高级功能
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "async.main-image.enhanced", havingValue = "true", matchIfMissing = false)
public class EnhancedAsyncMainImageUpdateServiceImpl implements IAsyncMainImageUpdateService {

    @Resource
    private OmgProductsMapper omgProductsMapper;

    @Resource
    private IMainImageProcessService mainImageProcessService;

    @Autowired
    private AsyncMainImageConfig config;

    // 处理结果缓存，避免重复处理相同SKU
    private final Map<String, MainImageProcessResult> resultCache =
            new ConcurrentHashMap<>();

    // 失败重试队列
    private final Queue<RetryTask> retryQueue = new ConcurrentLinkedQueue<>();

    /**
     * 异步批量更新商品主图
     */
    @Override
    @Async("taskExecutor")
    public void asyncBatchUpdateMainImages(List<OmgProducts> successfulProducts) {
        if (successfulProducts == null || successfulProducts.isEmpty()) {
            log.info("没有需要更新主图的商品");
            return;
        }

        log.info("开始增强版异步批量更新商品主图，商品数量: {}", successfulProducts.size());
        long startTime = System.currentTimeMillis();

        try {
            // 1. 预处理和去重
            List<OmgProducts> processedProducts = preprocessProducts(successfulProducts);
            
            // 2. 并行处理
            ProcessResult result = processProductsInParallel(processedProducts);
            
            // 3. 处理重试队列
            handleRetryQueue();
            
            // 4. 输出统计结果
            logFinalResults(successfulProducts.size(), result, startTime);
            
        } catch (Exception e) {
            log.error("增强版异步主图更新处理异常", e);
        } finally {
            // 清理缓存（可选）
            if (resultCache.size() > 1000) {
                resultCache.clear();
                log.info("清理主图处理结果缓存");
            }
        }
    }

    /**
     * 异步更新单个商品主图
     */
    @Override
    @Async("taskExecutor")
    public void asyncUpdateSingleMainImage(OmgProducts product) {
        if (product == null) {
            log.warn("商品信息为空，跳过主图更新");
            return;
        }

        try {
            ProcessResult result = processSingleProductWithRetry(product);
            log.info("单个商品主图更新完成，SKU: {}, 结果: {}", 
                    product.getSku(), result.isSuccess() ? "成功" : "失败");
        } catch (Exception e) {
            log.error("单个商品主图更新异常，SKU: {}", product.getSku(), e);
        }
    }

    /**
     * 根据平台名称获取对应的mallType
     */
    @Override
    public String getMallTypeByPlatform(String platform) {
        if (platform == null) {
            return null;
        }
        switch (platform.toLowerCase()) {
            case "淘宝":
            case "taobao":
                return "TAOBAO";
            case "1688":
                return "T1688";
            case "weidian":
            case "微店":
                return "WEIDIAN";
            default:
                return null;
        }
    }

    /**
     * 预处理商品列表
     */
    private List<OmgProducts> preprocessProducts(List<OmgProducts> products) {
        // 去重（基于SKU）
        Map<String, OmgProducts> uniqueProducts = new LinkedHashMap<>();
        
        for (OmgProducts product : products) {
            String sku = product.getSku();
            if (sku != null && !sku.trim().isEmpty()) {
                // 如果已存在相同SKU，保留第一个
                uniqueProducts.putIfAbsent(sku, product);
            }
        }
        
        // 过滤有效商品
        List<OmgProducts> validProducts = uniqueProducts.values().stream()
                .filter(this::isValidProduct)
                .collect(Collectors.toList());
        
        log.info("预处理完成，原始数量: {}, 去重后: {}, 有效商品: {}", 
                products.size(), uniqueProducts.size(), validProducts.size());
        
        return validProducts;
    }

    /**
     * 检查商品是否有效
     */
    private boolean isValidProduct(OmgProducts product) {
        String sku = product.getSku();
        String platform = product.getPlatform();
        
        if (sku == null || sku.trim().isEmpty()) {
            return false;
        }
        
        if (platform == null || platform.trim().isEmpty()) {
            return false;
        }
        
        return getMallTypeByPlatform(platform) != null;
    }

    /**
     * 并行处理商品
     */
    private ProcessResult processProductsInParallel(List<OmgProducts> products) {
        if (!config.isEnableParallel() || products.size() <= config.getBatchSize()) {
            // 串行处理
            return processProductsSequentially(products);
        }

        // 并行处理
        ExecutorService executor = Executors.newFixedThreadPool(config.getThreadPoolSize());
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failedCount = new AtomicInteger(0);
        
        try {
            List<List<OmgProducts>> batches = partitionList(products, config.getBatchSize());
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            
            for (List<OmgProducts> batch : batches) {
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    ProcessResult batchResult = processBatch(batch);
                    successCount.addAndGet(batchResult.getSuccessCount());
                    failedCount.addAndGet(batchResult.getFailedCount());
                }, executor);
                
                futures.add(future);
            }
            
            // 等待所有批次完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                    .get(config.getTimeoutSeconds(), TimeUnit.SECONDS);
            
        } catch (TimeoutException e) {
            log.error("并行处理超时", e);
        } catch (Exception e) {
            log.error("并行处理异常", e);
        } finally {
            executor.shutdown();
        }
        
        return new ProcessResult(successCount.get(), failedCount.get());
    }

    /**
     * 串行处理商品
     */
    private ProcessResult processProductsSequentially(List<OmgProducts> products) {
        int successCount = 0;
        int failedCount = 0;
        
        for (OmgProducts product : products) {
            ProcessResult result = processSingleProductWithRetry(product);
            if (result.isSuccess()) {
                successCount++;
            } else {
                failedCount++;
            }
        }
        
        return new ProcessResult(successCount, failedCount);
    }

    /**
     * 处理单个批次
     */
    private ProcessResult processBatch(List<OmgProducts> batch) {
        int successCount = 0;
        int failedCount = 0;
        
        for (OmgProducts product : batch) {
            ProcessResult result = processSingleProductWithRetry(product);
            if (result.isSuccess()) {
                successCount++;
            } else {
                failedCount++;
            }
            
            // 添加延迟
            if (config.getRequestDelay() > 0) {
                try {
                    Thread.sleep(config.getRequestDelay());
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
        
        return new ProcessResult(successCount, failedCount);
    }

    /**
     * 处理单个商品（带重试）
     */
    private ProcessResult processSingleProductWithRetry(OmgProducts product) {
        String sku = product.getSku();
        
        // 检查缓存
        if (resultCache.containsKey(sku)) {
            MainImageProcessResult cachedResult = resultCache.get(sku);
            if (cachedResult.isSuccess()) {
                updateProductMainImage(product, cachedResult.getMainImageUrl());
                return new ProcessResult(1, 0);
            }
        }
        
        // 尝试处理
        for (int attempt = 1; attempt <= config.getRetryCount(); attempt++) {
            try {
                String mallType = getMallTypeByPlatform(product.getPlatform());
                MainImageProcessResult result =
                        mainImageProcessService.processProductMainImage(product, mallType);
                
                // 缓存结果
                resultCache.put(sku, result);
                
                if (result.isSuccess() && result.getMainImageUrl() != null) {
                    updateProductMainImage(product, result.getMainImageUrl());
                    log.debug("SKU: {} 主图更新成功，尝试次数: {}", sku, attempt);
                    return new ProcessResult(1, 0);
                } else {
                    log.warn("SKU: {} 主图获取失败，尝试次数: {}, 原因: {}", sku, attempt, result.getMessage());
                }
                
            } catch (Exception e) {
                log.warn("SKU: {} 主图处理异常，尝试次数: {}", sku, attempt, e);
                
                if (attempt < config.getRetryCount()) {
                    try {
                        Thread.sleep(config.getRetryDelay());
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }
        
        // 所有重试都失败，加入重试队列
        retryQueue.offer(new RetryTask(product, System.currentTimeMillis()));
        return new ProcessResult(0, 1);
    }

    /**
     * 更新商品主图
     */
    private void updateProductMainImage(OmgProducts product, String mainImageUrl) {
        try {
            product.setMainImage(mainImageUrl);
            omgProductsMapper.updateOmgProducts(product);
        } catch (Exception e) {
            log.error("更新商品主图失败，SKU: {}", product.getSku(), e);
            throw e;
        }
    }

    /**
     * 处理重试队列
     */
    private void handleRetryQueue() {
        if (retryQueue.isEmpty()) {
            return;
        }
        
        log.info("开始处理重试队列，待重试商品数量: {}", retryQueue.size());
        
        int retrySuccess = 0;
        int retryFailed = 0;
        
        while (!retryQueue.isEmpty()) {
            RetryTask task = retryQueue.poll();
            if (task != null) {
                ProcessResult result = processSingleProductWithRetry(task.getProduct());
                if (result.isSuccess()) {
                    retrySuccess++;
                } else {
                    retryFailed++;
                }
            }
        }
        
        log.info("重试队列处理完成，成功: {}, 失败: {}", retrySuccess, retryFailed);
    }

    /**
     * 输出最终结果
     */
    private void logFinalResults(int totalCount, ProcessResult result, long startTime) {
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        log.info("增强版异步批量更新商品主图完成");
        log.info("总商品数: {}, 成功: {}, 失败: {}, 耗时: {}ms", 
                totalCount, result.getSuccessCount(), result.getFailedCount(), duration);
        log.info("平均处理时间: {}ms/商品", totalCount > 0 ? duration / totalCount : 0);
        log.info("缓存命中数: {}", resultCache.size());
    }

    /**
     * 分割列表
     */
    private <T> List<List<T>> partitionList(List<T> list, int batchSize) {
        List<List<T>> partitions = new ArrayList<>();
        for (int i = 0; i < list.size(); i += batchSize) {
            partitions.add(list.subList(i, Math.min(i + batchSize, list.size())));
        }
        return partitions;
    }

    /**
     * 处理结果类
     */
    private static class ProcessResult {
        private final int successCount;
        private final int failedCount;
        
        public ProcessResult(int successCount, int failedCount) {
            this.successCount = successCount;
            this.failedCount = failedCount;
        }
        
        public int getSuccessCount() { return successCount; }
        public int getFailedCount() { return failedCount; }
        public boolean isSuccess() { return successCount > 0 && failedCount == 0; }
    }

    /**
     * 重试任务类
     */
    private static class RetryTask {
        private final OmgProducts product;
        private final long timestamp;
        
        public RetryTask(OmgProducts product, long timestamp) {
            this.product = product;
            this.timestamp = timestamp;
        }
        
        public OmgProducts getProduct() { return product; }
        public long getTimestamp() { return timestamp; }
    }
}
