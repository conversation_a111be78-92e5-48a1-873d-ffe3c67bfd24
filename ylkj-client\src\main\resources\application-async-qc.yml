# 异步QC更新配置
async:
  qc-update:
    # 批处理大小（每批处理的商品数量）
    batch-size: 50
    
    # API请求延迟时间（毫秒，避免请求过于频繁）
    request-delay: 200
    
    # 任务状态缓存过期时间（小时）
    task-status-expire-hours: 24
    
    # 是否启用批量数据库更新
    enable-batch-update: true
    
    # 超时时间（秒）
    timeout-seconds: 3600
    
    # 重试次数
    retry-count: 3
    
    # 重试延迟时间（毫秒）
    retry-delay: 5000

# 线程池配置优化（专门用于QC更新）
spring:
  task:
    execution:
      pool:
        # 核心线程数
        core-size: 3
        # 最大线程数  
        max-size: 8
        # 队列容量
        queue-capacity: 100
        # 线程名前缀
        thread-name-prefix: "async-qc-"
        # 线程空闲时间
        keep-alive: 300s
      shutdown:
        # 优雅关闭
        await-termination: true
        await-termination-period: 120s
