package com.ylkj.service.impl.omg;

import com.ylkj.model.domain.omg.OPlatformLinks;
import com.ylkj.mapper.omg.OPlatformLinksMapper;
import com.ylkj.service.omg.IOPlatformLinksService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Iterator;
import java.util.List;

/**
 * <p>
 * omg平台链接表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
@Service
public class OPlatformLinksServiceImpl extends ServiceImpl<OPlatformLinksMapper, OPlatformLinks> implements IOPlatformLinksService {

    @Autowired
    private OPlatformLinksMapper oPlatformLinksMapper;

    @Override
    public List<OPlatformLinks> getOPlatformLinks() {
        List<OPlatformLinks> oPlatformLinks =oPlatformLinksMapper.getOPlatformLinks();
        Iterator<OPlatformLinks> item = oPlatformLinks.iterator();
        while (item.hasNext()){
            OPlatformLinks next = item.next();
            if (next.getConcatenateFiled() != null && !next.getConcatenateFiled().isEmpty()){
                String urlTemplate = next.getUrltemplate() + next.getConcatenateFiled();
                next.setUrltemplate(urlTemplate);
            }
        }
        return oPlatformLinks;
    }
}
