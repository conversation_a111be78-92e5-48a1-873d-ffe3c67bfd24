package com.ylkj.service.omg;

import com.ylkj.model.domain.Products;
import com.ylkj.model.domain.omg.OProducts;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ylkj.model.vo.OProductsVO;
import com.ylkj.model.vo.ProductsVo;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * omg_商品表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
public interface OIProductsService extends IService<OProducts> {

    /**
     * 搜索商品
     *
     * @param oProductsVo 商品搜索参数
     * @return 搜索结果列表
     */
    List<OProducts> searchProducts(OProductsVO oProductsVo);

    /**
     * 根据分类ID获取商品列表
     *
     * @param categoryId 分类ID
     * @param brandId 品牌ID
     * @param merchant 商家名称
     * @return 商品列表
     */
    List<OProducts> getProductsByCategory(Long categoryId,Long brandId,String merchant);

    /**
     * 根据商品ID获取商品详情
     *
     * @param id 商品ID
     * @return 商品详情
     */
    OProductsVO getProductById(Long id);

    /**
     * 更新商品浏览量
     *
     * @param productId 商品ID
     * @return 更新结果
     */
    int updateViews(Long productId);

    /**
     * 获取推荐商品列表
     *
     * @param products 商品参数
     * @return 推荐商品列表
     */
    List<OProducts> getRecommendProducts(Products products);

    /**
     * 获取所有商家名称
     *
     * @return 商家名称列表
     */
    List<String> getAllMerchant();
    
    /**
     * 批量更新所有商品的QC图片数量
     * 
     * @return 更新成功的商品数量
     */
    int batchUpdateProductsQcCount();
    
    /**
     * 分页批量更新商品的QC图片数量
     * 
     * @param pageSize 每页处理的商品数量
     * @return 更新成功的商品数量
     */
    int batchUpdateProductsQcCountWithPaging(int pageSize);
    
    /**
     * 根据SKU列表批量更新商品的QC图片数量
     * 
     * @param skuList 要更新的SKU列表
     * @return 更新成功的商品数量
     */
    int batchUpdateProductsQcCountBySku(List<String> skuList);

    /**
     * 根据状态批量获取商品列表
     *
     * @param statusList 要获取的商品状态列表
     * @return 商品列表
     */
    List<OProducts> getProductsByStatus(List<Integer> statusList);

    /**
     * 清除商品详情缓存
     * 
     * @param sku 商品SKU
     * @param platform 平台名称
     * @return 是否清除成功
     */
    boolean clearProductDetailCache(String sku, String platform);

    /**
     * 检查商品详情缓存是否存在
     * 
     * @param sku 商品SKU
     * @param platform 平台名称
     * @return 是否存在缓存
     */
    boolean hasProductDetailCache(String sku, String platform);

    /**
     * 清除所有商品详情缓存
     * 
     * @return 清除的缓存数量
     */
    int clearAllProductDetailCache();

    /**
     * 获取所有商品数量
     *
     * @return 商品数量
     */
    List<OProducts> getAllProductsCount();

    /**
     * 获取异步QC更新任务状态
     *
     * @param taskId 任务ID
     * @return 任务状态信息
     */
    Object getQcUpdateTaskStatus(String taskId);
}
