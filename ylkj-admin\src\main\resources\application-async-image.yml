# 异步主图更新配置示例
async:
  main-image:
    # 是否启用增强版异步主图更新服务
    enhanced: true
    
    # 批处理大小（每批处理的商品数量）
    batch-size: 20
    
    # 线程池大小（并行处理的线程数）
    thread-pool-size: 5
    
    # 请求延迟时间（毫秒，避免请求过于频繁）
    request-delay: 1000
    
    # 是否启用并行处理
    enable-parallel: true
    
    # 是否启用预过滤
    enable-pre-filter: true
    
    # 是否启用批量数据库更新
    enable-batch-update: false
    
    # 超时时间（秒）
    timeout-seconds: 300
    
    # 重试次数
    retry-count: 3
    
    # 重试延迟时间（毫秒）
    retry-delay: 5000

# 线程池配置优化
spring:
  task:
    execution:
      pool:
        # 核心线程数
        core-size: 5
        # 最大线程数  
        max-size: 10
        # 队列容量
        queue-capacity: 200
        # 线程名前缀
        thread-name-prefix: "async-image-"
        # 线程空闲时间
        keep-alive: 60s
      shutdown:
        # 优雅关闭
        await-termination: true
        await-termination-period: 60s
